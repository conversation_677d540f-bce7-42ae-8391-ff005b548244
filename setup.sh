#!/bin/bash

# Setup script for GPT Wrapper
echo "🚀 Setting up GPT Wrapper..."

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "❌ Python is not installed. Please install Python 3.8+ first."
    exit 1
fi

# Check Python version
python_version=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "✓ Python $python_version detected"

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✓ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✓ .env file created from template"
    echo "⚠️  Please edit .env and add your OPENAI_API_KEY"
else
    echo "✓ .env file already exists"
fi

# Run tests
echo "🧪 Running tests..."
python test_gpt_wrapper.py

if [ $? -eq 0 ]; then
    echo "✓ All tests passed!"
else
    echo "❌ Some tests failed"
    exit 1
fi

# Run demo
echo "🎯 Running demo..."
python demo.py

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your OPENAI_API_KEY"
echo "2. Run: python test_installation.py (to verify API key)"
echo "3. Run: python example.py (to see usage examples)"
echo "4. Check README.md for detailed documentation"
