"""
Simple Python wrapper for OpenAI's GPT API with token usage limits and cost control.
"""
import time
from typing import Dict, List, Optional, Any, Union
import tiktoken
from openai import OpenAI

from config import (
    DEFAULT_CONFIG,
    get_openai_api_key,
    estimate_cost,
    TOKEN_COSTS,
)
from exceptions import (
    TokenLimitExceededError,
    CostLimitExceededError,
    InvalidConfigurationError,
    APIKeyError,
)


class GPTWrapper:
    """
    A simple wrapper around OpenAI's GPT API with token usage limits and cost control.
    
    Features:
    - Token usage tracking and limits
    - Cost estimation and control
    - Session-based token counting
    - Configurable defaults with override capability
    """
    
    def __init__(self, **config_overrides):
        """
        Initialize the GPT wrapper.
        
        Args:
            **config_overrides: Configuration overrides for default settings
        """
        # Merge default config with overrides
        self.config = {**DEFAULT_CONFIG, **config_overrides}
        
        # Initialize OpenAI client
        try:
            self.client = OpenAI(api_key=get_openai_api_key())
        except ValueError as e:
            raise APIKeyError(str(e))
        
        # Initialize session tracking
        self.session_tokens_used = 0
        self.session_cost = 0.0
        self.request_count = 0
        
        # Initialize tokenizer for the default model
        self._update_tokenizer()
    
    def _update_tokenizer(self):
        """Update the tokenizer based on the current model."""
        try:
            self.tokenizer = tiktoken.encoding_for_model(self.config["model"])
        except KeyError:
            # Fallback to cl100k_base for unknown models
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    def _count_tokens(self, text: str) -> int:
        """Count tokens in a text string."""
        return len(self.tokenizer.encode(text))
    
    def _count_message_tokens(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in a list of messages."""
        total_tokens = 0
        for message in messages:
            # Add tokens for role and content
            total_tokens += self._count_tokens(message.get("role", ""))
            total_tokens += self._count_tokens(message.get("content", ""))
            # Add overhead tokens per message (varies by model)
            total_tokens += 4  # Approximate overhead per message
        
        total_tokens += 2  # Overhead for the conversation
        return total_tokens
    
    def _check_token_limits(self, estimated_tokens: int):
        """Check if the request would exceed token limits."""
        # Check per-request limit
        if estimated_tokens > self.config["max_tokens_per_request"]:
            raise TokenLimitExceededError(
                f"Request would use {estimated_tokens} tokens, "
                f"exceeding per-request limit of {self.config['max_tokens_per_request']}"
            )
        
        # Check session limit
        if self.session_tokens_used + estimated_tokens > self.config["max_tokens_per_session"]:
            raise TokenLimitExceededError(
                f"Request would use {estimated_tokens} tokens, "
                f"exceeding session limit of {self.config['max_tokens_per_session']} "
                f"(current session usage: {self.session_tokens_used})"
            )
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a chat completion with token and cost tracking.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use (overrides default)
            max_tokens: Maximum tokens for response (overrides default)
            temperature: Temperature for response (overrides default)
            **kwargs: Additional parameters to pass to OpenAI API
        
        Returns:
            Dictionary containing response and usage information
        """
        # Use provided parameters or fall back to config defaults
        model = model or self.config["model"]
        max_tokens = max_tokens or self.config["max_tokens_per_request"]
        temperature = temperature if temperature is not None else self.config["temperature"]
        
        # Update tokenizer if model changed
        if model != self.config["model"]:
            old_model = self.config["model"]
            self.config["model"] = model
            self._update_tokenizer()
        
        # Estimate input tokens
        input_tokens = self._count_message_tokens(messages)
        estimated_total_tokens = input_tokens + max_tokens
        
        # Check token limits
        self._check_token_limits(estimated_total_tokens)
        
        try:
            # Make the API call
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            # Extract usage information
            usage = response.usage
            actual_input_tokens = usage.prompt_tokens
            actual_output_tokens = usage.completion_tokens
            total_tokens = usage.total_tokens
            
            # Calculate cost
            cost = estimate_cost(actual_input_tokens, actual_output_tokens, model)
            
            # Update session tracking
            self.session_tokens_used += total_tokens
            self.session_cost += cost
            self.request_count += 1
            
            # Return structured response
            return {
                "response": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": actual_input_tokens,
                    "completion_tokens": actual_output_tokens,
                    "total_tokens": total_tokens,
                    "estimated_cost": cost,
                },
                "session_stats": {
                    "total_tokens_used": self.session_tokens_used,
                    "total_cost": self.session_cost,
                    "request_count": self.request_count,
                    "remaining_tokens": self.config["max_tokens_per_session"] - self.session_tokens_used,
                },
                "model": model,
            }
            
        except Exception as e:
            # Re-raise OpenAI API errors
            raise e
    
    def simple_chat(self, prompt: str, **kwargs) -> str:
        """
        Simple chat interface for single prompts.
        
        Args:
            prompt: The user prompt
            **kwargs: Additional parameters for chat_completion
        
        Returns:
            The AI response as a string
        """
        messages = [{"role": "user", "content": prompt}]
        result = self.chat_completion(messages, **kwargs)
        return result["response"]
    
    def reset_session(self):
        """Reset session token and cost tracking."""
        self.session_tokens_used = 0
        self.session_cost = 0.0
        self.request_count = 0
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get current session statistics."""
        return {
            "total_tokens_used": self.session_tokens_used,
            "total_cost": self.session_cost,
            "request_count": self.request_count,
            "remaining_tokens": self.config["max_tokens_per_session"] - self.session_tokens_used,
            "model": self.config["model"],
            "max_tokens_per_request": self.config["max_tokens_per_request"],
            "max_tokens_per_session": self.config["max_tokens_per_session"],
        }
    
    def update_config(self, **new_config):
        """Update configuration settings."""
        self.config.update(new_config)
        if "model" in new_config:
            self._update_tokenizer()
