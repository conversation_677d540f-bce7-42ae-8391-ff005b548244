{"name": "suno-api", "description": "Use API to call the music generation service of suno.ai, and easily integrate it into agents like GPTs.", "author": {"name": "gcui.ai", "url": "https://github.com/gcui-art/"}, "license": "LGPL-3.0-or-later", "version": "1.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@2captcha/captcha-solver": "^1.3.0", "@playwright/browser-chromium": "^1.49.1", "@vercel/analytics": "^1.2.2", "axios": "^1.7.8", "bufferutil": "^4.0.8", "chromium-bidi": "^0.10.1", "cookie": "^1.0.2", "electron": "^33.2.1", "ghost-cursor-playwright": "^2.1.0", "js-cookie": "^3.0.5", "next": "14.1.4", "next-swagger-doc": "^0.4.0", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "rebrowser-playwright-core": "^1.49.1", "swagger-ui-react": "^5.18.2", "tough-cookie": "^4.1.4", "user-agents": "^1.1.156", "utf-8-validate": "^6.0.5", "yn": "^5.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.12", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/swagger-ui-react": "^4.18.3", "@types/tough-cookie": "^4.0.5", "@types/user-agents": "^1.0.4", "autoprefixer": "^10.0.1", "eslint": "^8.57.0", "eslint-config-next": "14.1.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}