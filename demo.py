#!/usr/bin/env python3
"""
Demo script showing the GPT wrapper functionality without requiring an API key.
This demonstrates the cost estimation and token counting features.
"""

from config import estimate_cost, TOKEN_COSTS, DEFAULT_CONFIG
from exceptions import TokenLimitExceededError
import tiktoken

def demo_cost_estimation():
    """Demonstrate cost estimation for different models."""
    print("🔍 Cost Estimation Demo")
    print("=" * 50)
    
    # Example token counts
    input_tokens = 1000
    output_tokens = 500
    
    print(f"Example: {input_tokens} input tokens + {output_tokens} output tokens\n")
    
    for model, costs in TOKEN_COSTS.items():
        total_cost = estimate_cost(input_tokens, output_tokens, model)
        print(f"{model:15} | ${total_cost:.6f}")
    
    print("\n💡 Tip: Use gpt-4o-mini for the best cost/performance ratio!")

def demo_token_counting():
    """Demonstrate token counting functionality."""
    print("\n🔢 Token Counting Demo")
    print("=" * 50)
    
    # Initialize tokenizer
    tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")
    
    examples = [
        "Hello, world!",
        "This is a longer sentence with more words to demonstrate token counting.",
        "Python is a programming language that is easy to learn and powerful to use.",
        "The quick brown fox jumps over the lazy dog. This is a classic pangram used in typography.",
    ]
    
    for text in examples:
        tokens = len(tokenizer.encode(text))
        cost = estimate_cost(tokens, 0, "gpt-3.5-turbo")  # Input only
        print(f"Text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        print(f"Tokens: {tokens} | Input cost: ${cost:.6f}\n")

def demo_session_limits():
    """Demonstrate session limit calculations."""
    print("⚠️  Session Limits Demo")
    print("=" * 50)
    
    config = DEFAULT_CONFIG
    print(f"Default Configuration:")
    print(f"  Max tokens per request: {config['max_tokens_per_request']}")
    print(f"  Max tokens per session: {config['max_tokens_per_session']}")
    print(f"  Default model: {config['model']}")
    
    # Simulate session usage
    session_tokens = 0
    request_count = 0
    total_cost = 0.0

    print(f"\n📊 Simulated Session Usage:")

    # Simulate several requests
    requests = [250, 400, 300, 500, 200]

    for i, tokens in enumerate(requests, 1):
        if session_tokens + tokens > config['max_tokens_per_session']:
            print(f"Request {i}: {tokens} tokens - ❌ WOULD EXCEED SESSION LIMIT")
            print(f"  Current usage: {session_tokens}/{config['max_tokens_per_session']} tokens")
            print(f"  Would need: {session_tokens + tokens} tokens")
            break
        else:
            session_tokens += tokens
            request_count += 1
            cost = estimate_cost(tokens // 2, tokens // 2, config['model'])  # Assume 50/50 split
            total_cost += cost
            
            print(f"Request {i}: {tokens} tokens - ✅ OK")
            print(f"  Session total: {session_tokens}/{config['max_tokens_per_session']} tokens")
            print(f"  Request cost: ${cost:.6f}")
            print(f"  Total cost: ${total_cost:.6f}")
            print()
    
    remaining = config['max_tokens_per_session'] - session_tokens
    print(f"Final session stats:")
    print(f"  Requests completed: {request_count}")
    print(f"  Tokens remaining: {remaining}")
    print(f"  Total estimated cost: ${total_cost:.6f}")

def demo_model_comparison():
    """Compare different models for cost and capability."""
    print("\n🤖 Model Comparison")
    print("=" * 50)
    
    # Example: 1000 word essay (approximately 1300 tokens input, 1000 tokens output)
    input_tokens = 1300
    output_tokens = 1000
    
    print(f"Example: Writing a 1000-word essay")
    print(f"Estimated tokens: {input_tokens} input + {output_tokens} output\n")
    
    models_info = {
        "gpt-3.5-turbo": "Fast, cost-effective, good for most tasks",
        "gpt-4o-mini": "Best cost/performance ratio, very capable",
        "gpt-4o": "High capability, balanced cost",
        "gpt-4-turbo": "Very capable, moderate cost",
        "gpt-4": "Most capable, highest cost"
    }
    
    costs = []
    for model in TOKEN_COSTS.keys():
        cost = estimate_cost(input_tokens, output_tokens, model)
        costs.append((model, cost, models_info[model]))
    
    # Sort by cost
    costs.sort(key=lambda x: x[1])
    
    print("Models ranked by cost (cheapest first):")
    for i, (model, cost, description) in enumerate(costs, 1):
        print(f"{i}. {model:15} | ${cost:.6f} | {description}")

def main():
    """Run all demos."""
    print("🚀 GPT Wrapper Demo")
    print("This demo shows the wrapper's features without requiring an API key.\n")
    
    demo_cost_estimation()
    demo_token_counting()
    demo_session_limits()
    demo_model_comparison()
    
    print("\n" + "=" * 50)
    print("✨ Ready to use the GPT wrapper!")
    print("Next steps:")
    print("1. Set up your API key in .env file")
    print("2. Run: python example.py")
    print("3. Check README.md for more examples")

if __name__ == "__main__":
    main()
