"""
Custom exceptions for the GPT API wrapper.
"""


class GPTWrapperError(Exception):
    """Base exception for GPT wrapper errors."""
    pass


class TokenLimitExceededError(GPTWrapperError):
    """Raised when token limits are exceeded."""
    pass


class CostLimitExceededError(GPTWrapperError):
    """Raised when cost limits are exceeded."""
    pass


class InvalidConfigurationError(GPTWrapperError):
    """Raised when configuration is invalid."""
    pass


class APIKeyError(GPTWrapperError):
    """Raised when API key is missing or invalid."""
    pass
