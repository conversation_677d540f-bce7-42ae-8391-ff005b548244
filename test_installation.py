#!/usr/bin/env python3
"""
Test script to verify the GPT wrapper installation and basic functionality.
Run this after setting up your API key to ensure everything works.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    try:
        from gpt_wrapper import GPTWrapper
        from config import DEFAULT_CONFIG, TOKEN_COSTS
        from exceptions import TokenLimitExceededError, APIKeyError
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_api_key():
    """Test API key configuration."""
    print("\nTesting API key configuration...")
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("✗ OPENAI_API_KEY not found in environment")
        print("  Please create a .env file with your API key")
        return False
    elif api_key == "your_openai_api_key_here":
        print("✗ OPENAI_API_KEY is still the placeholder value")
        print("  Please update your .env file with a real API key")
        return False
    else:
        print("✓ API key found in environment")
        return True

def test_initialization():
    """Test wrapper initialization."""
    print("\nTesting wrapper initialization...")
    try:
        from gpt_wrapper import GPTWrapper
        
        # Test with default config
        gpt = GPTWrapper()
        print("✓ Default initialization successful")
        
        # Test with custom config
        gpt_custom = GPTWrapper(
            model="gpt-3.5-turbo",
            max_tokens_per_request=100,
            temperature=0.5
        )
        print("✓ Custom configuration successful")
        
        return True
    except Exception as e:
        print(f"✗ Initialization error: {e}")
        return False

def test_token_counting():
    """Test token counting functionality."""
    print("\nTesting token counting...")
    try:
        from gpt_wrapper import GPTWrapper
        
        gpt = GPTWrapper()
        
        # Test simple token counting
        tokens = gpt._count_tokens("Hello, world!")
        print(f"✓ Token counting works: 'Hello, world!' = {tokens} tokens")
        
        # Test message token counting
        messages = [{"role": "user", "content": "Test message"}]
        message_tokens = gpt._count_message_tokens(messages)
        print(f"✓ Message token counting works: {message_tokens} tokens")
        
        return True
    except Exception as e:
        print(f"✗ Token counting error: {e}")
        return False

def test_session_management():
    """Test session management features."""
    print("\nTesting session management...")
    try:
        from gpt_wrapper import GPTWrapper
        
        gpt = GPTWrapper()
        
        # Test initial stats
        stats = gpt.get_session_stats()
        assert stats['total_tokens_used'] == 0
        assert stats['total_cost'] == 0.0
        assert stats['request_count'] == 0
        print("✓ Initial session stats correct")
        
        # Test session reset
        gpt.session_tokens_used = 100
        gpt.session_cost = 0.05
        gpt.request_count = 2
        
        gpt.reset_session()
        stats = gpt.get_session_stats()
        assert stats['total_tokens_used'] == 0
        assert stats['total_cost'] == 0.0
        assert stats['request_count'] == 0
        print("✓ Session reset works correctly")
        
        return True
    except Exception as e:
        print(f"✗ Session management error: {e}")
        return False

def test_cost_estimation():
    """Test cost estimation functionality."""
    print("\nTesting cost estimation...")
    try:
        from config import estimate_cost, TOKEN_COSTS
        
        # Test cost estimation for different models
        for model in TOKEN_COSTS.keys():
            cost = estimate_cost(1000, 500, model)
            print(f"✓ {model}: 1000 input + 500 output tokens = ${cost:.6f}")
        
        return True
    except Exception as e:
        print(f"✗ Cost estimation error: {e}")
        return False

def main():
    """Run all tests."""
    print("GPT Wrapper Installation Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_api_key,
        test_initialization,
        test_token_counting,
        test_session_management,
        test_cost_estimation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'=' * 40}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The wrapper is ready to use.")
        print("\nNext steps:")
        print("1. Try running: python example.py")
        print("2. Check the README.md for usage examples")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        if not os.getenv("OPENAI_API_KEY"):
            print("\nMost likely issue: Missing API key")
            print("1. Copy .env.example to .env")
            print("2. Add your OpenAI API key to the .env file")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
