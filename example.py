"""
Example usage of the GPT API wrapper.
"""
import os
from dotenv import load_dotenv
from gpt_wrapper import GP<PERSON><PERSON><PERSON>per
from exceptions import TokenLimitExceededError, APIKeyError

# Load environment variables
load_dotenv()


def basic_usage_example():
    """Basic usage example."""
    print("=== Basic Usage Example ===")
    
    try:
        # Initialize with default settings
        gpt = GPTWrapper()
        
        # Simple chat
        response = gpt.simple_chat("What is the capital of France?")
        print(f"Response: {response}")
        
        # Get session stats
        stats = gpt.get_session_stats()
        print(f"Session stats: {stats}")
        
    except APIKeyError as e:
        print(f"API Key Error: {e}")
        print("Please set your OPENAI_API_KEY in a .env file")
    except Exception as e:
        print(f"Error: {e}")


def custom_configuration_example():
    """Example with custom configuration."""
    print("\n=== Custom Configuration Example ===")
    
    try:
        # Initialize with custom settings
        gpt = GPTWrapper(
            model="gpt-4o-mini",  # More cost-effective model
            max_tokens_per_request=500,
            max_tokens_per_session=5000,
            temperature=0.3
        )
        
        # Chat with conversation history
        messages = [
            {"role": "system", "content": "You are a helpful assistant that gives concise answers."},
            {"role": "user", "content": "Explain quantum computing in simple terms."}
        ]
        
        result = gpt.chat_completion(messages, max_tokens=200)
        
        print(f"Response: {result['response']}")
        print(f"Tokens used: {result['usage']['total_tokens']}")
        print(f"Estimated cost: ${result['usage']['estimated_cost']:.6f}")
        print(f"Remaining session tokens: {result['session_stats']['remaining_tokens']}")
        
    except Exception as e:
        print(f"Error: {e}")


def token_limit_example():
    """Example demonstrating token limits."""
    print("\n=== Token Limit Example ===")
    
    try:
        # Initialize with very low limits for demonstration
        gpt = GPTWrapper(
            max_tokens_per_request=50,
            max_tokens_per_session=200
        )
        
        # This should work
        response1 = gpt.simple_chat("Hi!")
        print(f"First response: {response1}")
        
        # This might exceed limits
        long_prompt = "Write a detailed essay about artificial intelligence, machine learning, and their impact on society. Include historical context, current applications, and future predictions." * 3
        
        try:
            response2 = gpt.simple_chat(long_prompt)
            print(f"Second response: {response2}")
        except TokenLimitExceededError as e:
            print(f"Token limit exceeded: {e}")
            
            # Reset session and try again with shorter prompt
            gpt.reset_session()
            print("Session reset. Trying with shorter prompt...")
            
            shorter_prompt = "Briefly explain AI."
            response3 = gpt.simple_chat(shorter_prompt)
            print(f"Third response: {response3}")
        
    except Exception as e:
        print(f"Error: {e}")


def conversation_example():
    """Example of a multi-turn conversation."""
    print("\n=== Conversation Example ===")
    
    try:
        gpt = GPTWrapper(model="gpt-3.5-turbo")
        
        # Start a conversation
        messages = [
            {"role": "system", "content": "You are a helpful coding assistant."}
        ]
        
        # First turn
        messages.append({"role": "user", "content": "How do I create a Python list?"})
        result1 = gpt.chat_completion(messages)
        messages.append({"role": "assistant", "content": result1["response"]})
        
        print(f"User: How do I create a Python list?")
        print(f"Assistant: {result1['response']}")
        print(f"Cost so far: ${result1['session_stats']['total_cost']:.6f}")
        
        # Second turn
        messages.append({"role": "user", "content": "Can you show me an example with numbers?"})
        result2 = gpt.chat_completion(messages)
        
        print(f"\nUser: Can you show me an example with numbers?")
        print(f"Assistant: {result2['response']}")
        print(f"Total cost: ${result2['session_stats']['total_cost']:.6f}")
        print(f"Total tokens: {result2['session_stats']['total_tokens_used']}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    # Run examples
    basic_usage_example()
    custom_configuration_example()
    token_limit_example()
    conversation_example()
    
    print("\n=== Available Models and Pricing ===")
    from config import TOKEN_COSTS
    for model, costs in TOKEN_COSTS.items():
        print(f"{model}: ${costs['input']}/1K input tokens, ${costs['output']}/1K output tokens")
