# GPT Experiment

A simple, barebones Python wrapper for OpenAI's GPT API with built-in token usage limits and cost control features.

## Features

- **Token Usage Tracking**: Monitor tokens used per request and per session
- **Cost Control**: Automatic cost estimation and limits
- **Configurable Defaults**: Sensible defaults with easy override capability
- **Session Management**: Track usage across multiple requests
- **Error Handling**: Custom exceptions for different error scenarios
- **Multiple Models**: Support for all OpenAI GPT models with pricing awareness

## Quick Start

1. **Install dependencies**:
```bash
pip install -r requirements.txt
```

2. **Set up your OpenAI API key**:
```bash
cp .env.example .env
# Edit .env and add your OPENAI_API_KEY
```

3. **Basic usage**:
```python
from gpt_wrapper import GPTWrapper

# Initialize with default settings
gpt = GPTWrapper()

# Simple chat
response = gpt.simple_chat("What is the capital of France?")
print(response)

# Get usage statistics
stats = gpt.get_session_stats()
print(f"Tokens used: {stats['total_tokens_used']}")
print(f"Estimated cost: ${stats['total_cost']:.6f}")
```

## Configuration

### Default Settings (with sensible cost control)

- **Max tokens per request**: 1000
- **Max tokens per session**: 10000
- **Default model**: gpt-4o-mini (most cost-effective)
- **Temperature**: 0.7
- **Max retries**: 3
- **Timeout**: 30 seconds

### Custom Configuration

```python
# Initialize with custom settings for better cost control
gpt = GPTWrapper(
    model="gpt-4o-mini",           # More cost-effective model
    max_tokens_per_request=500,    # Lower per-request limit
    max_tokens_per_session=5000,   # Lower session limit
    temperature=0.3                # More deterministic responses
)
```

## Usage Examples

### Conversation with Context

```python
# Multi-turn conversation
messages = [
    {"role": "system", "content": "You are a helpful coding assistant."},
    {"role": "user", "content": "How do I create a Python list?"}
]

result = gpt.chat_completion(messages)
print(f"Response: {result['response']}")
print(f"Tokens used: {result['usage']['total_tokens']}")
print(f"Cost: ${result['usage']['estimated_cost']:.6f}")
```

### Token Limit Management

```python
from exceptions import TokenLimitExceededError

try:
    response = gpt.simple_chat("Very long prompt...")
except TokenLimitExceededError as e:
    print(f"Token limit exceeded: {e}")

    # Reset session and try again
    gpt.reset_session()
    response = gpt.simple_chat("Shorter prompt")
```

## Supported Models and Current Pricing

- **gpt-3.5-turbo**: $0.0015/1K input, $0.002/1K output tokens
- **gpt-4o-mini**: $0.00015/1K input, $0.0006/1K output tokens
- **gpt-4o**: $0.005/1K input, $0.015/1K output tokens
- **gpt-4-turbo**: $0.01/1K input, $0.03/1K output tokens
- **gpt-4**: $0.03/1K input, $0.06/1K output tokens

*Note: Pricing is subject to change. Check OpenAI's official pricing for the most current rates.*

## Running Tests

```bash
python test_gpt_wrapper.py
```

## Running Examples

```bash
python example.py
```

## Files

- `gpt_wrapper.py` - Main wrapper class
- `config.py` - Configuration and pricing information
- `exceptions.py` - Custom exceptions
- `example.py` - Usage examples
- `test_gpt_wrapper.py` - Unit tests
- `requirements.txt` - Python dependencies
- `.env.example` - Environment variable template