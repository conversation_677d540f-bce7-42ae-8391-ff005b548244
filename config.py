"""
Configuration settings for the GPT API wrapper.
"""
import os
from typing import Dict, Any

# Default token limits
DEFAULT_MAX_TOKENS_PER_REQUEST = 1200
DEFAULT_MAX_TOKENS_PER_SESSION = 10000
DEFAULT_MODEL = "gpt-3.5-turbo"

# Token costs per 1K tokens (as of 2024, subject to change)
TOKEN_COSTS = {
    "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
    "gpt-4": {"input": 0.03, "output": 0.06},
    "gpt-4-turbo": {"input": 0.01, "output": 0.03},
    "gpt-4o": {"input": 0.005, "output": 0.015},
    "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
}

# Default configuration
DEFAULT_CONFIG = {
    "max_tokens_per_request": DEFAULT_MAX_TOKENS_PER_REQUEST,
    "max_tokens_per_session": DEFAULT_MAX_TOKENS_PER_SESSION,
    "model": DEFAULT_MODEL,
    "temperature": 0.7,
    "max_retries": 3,
    "timeout": 30,
}


def get_openai_api_key() -> str:
    """Get OpenAI API key from environment variables."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise ValueError(
            "OPENAI_API_KEY environment variable is required. "
            "Please set it in your .env file or environment."
        )
    return api_key


def estimate_cost(input_tokens: int, output_tokens: int, model: str) -> float:
    """Estimate the cost of a request based on token usage."""
    if model not in TOKEN_COSTS:
        # Default to gpt-3.5-turbo pricing for unknown models
        model = "gpt-3.5-turbo"
    
    costs = TOKEN_COSTS[model]
    input_cost = (input_tokens / 1000) * costs["input"]
    output_cost = (output_tokens / 1000) * costs["output"]
    
    return input_cost + output_cost
