"""
Unit tests for the GPT API wrapper.
"""
import unittest
from unittest.mock import <PERSON><PERSON>, patch, MagicMock
import sys
import os

from gpt_wrapper import GP<PERSON><PERSON>rapper
from exceptions import TokenLimitExceededError, APIKeyError
from config import DEFAULT_CONFIG


class TestGPTWrapper(unittest.TestCase):
    """Test cases for GPTWrapper class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock the OpenAI client to avoid actual API calls
        self.mock_openai_patcher = patch('gpt_wrapper.OpenAI')
        self.mock_openai = self.mock_openai_patcher.start()
        
        # Mock the API key function
        self.mock_api_key_patcher = patch('gpt_wrapper.get_openai_api_key')
        self.mock_get_api_key = self.mock_api_key_patcher.start()
        self.mock_get_api_key.return_value = "test-api-key"
        
        # Mock tiktoken
        self.mock_tiktoken_patcher = patch('gpt_wrapper.tiktoken')
        self.mock_tiktoken = self.mock_tiktoken_patcher.start()
        
        # Create a mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.encode.return_value = [1, 2, 3, 4, 5]  # 5 tokens
        self.mock_tiktoken.encoding_for_model.return_value = mock_tokenizer
        self.mock_tiktoken.get_encoding.return_value = mock_tokenizer
    
    def tearDown(self):
        """Clean up test fixtures."""
        self.mock_openai_patcher.stop()
        self.mock_api_key_patcher.stop()
        self.mock_tiktoken_patcher.stop()
    
    def test_initialization_default_config(self):
        """Test initialization with default configuration."""
        wrapper = GPTWrapper()
        
        self.assertEqual(wrapper.config["model"], DEFAULT_CONFIG["model"])
        self.assertEqual(wrapper.config["max_tokens_per_request"], DEFAULT_CONFIG["max_tokens_per_request"])
        self.assertEqual(wrapper.session_tokens_used, 0)
        self.assertEqual(wrapper.session_cost, 0.0)
        self.assertEqual(wrapper.request_count, 0)
    
    def test_initialization_custom_config(self):
        """Test initialization with custom configuration."""
        custom_config = {
            "model": "gpt-4",
            "max_tokens_per_request": 500,
            "temperature": 0.5
        }
        
        wrapper = GPTWrapper(**custom_config)
        
        self.assertEqual(wrapper.config["model"], "gpt-4")
        self.assertEqual(wrapper.config["max_tokens_per_request"], 500)
        self.assertEqual(wrapper.config["temperature"], 0.5)
        # Should keep default values for non-overridden settings
        self.assertEqual(wrapper.config["max_tokens_per_session"], DEFAULT_CONFIG["max_tokens_per_session"])
    
    def test_api_key_error(self):
        """Test that APIKeyError is raised when API key is missing."""
        self.mock_get_api_key.side_effect = ValueError("API key missing")
        
        with self.assertRaises(APIKeyError):
            GPTWrapper()
    
    def test_token_counting(self):
        """Test token counting functionality."""
        wrapper = GPTWrapper()
        
        # Mock tokenizer returns 5 tokens for any input
        tokens = wrapper._count_tokens("test message")
        self.assertEqual(tokens, 5)
    
    def test_message_token_counting(self):
        """Test token counting for message lists."""
        wrapper = GPTWrapper()
        
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there"}
        ]
        
        # Each message: 5 tokens (content) + 5 tokens (role) + 4 overhead = 14 tokens per message
        # Plus 2 tokens conversation overhead = 14 * 2 + 2 = 30 tokens
        tokens = wrapper._count_message_tokens(messages)
        self.assertEqual(tokens, 30)
    
    def test_token_limit_per_request(self):
        """Test per-request token limit enforcement."""
        wrapper = GPTWrapper(max_tokens_per_request=10)
        
        # This should exceed the limit (estimated 15 tokens > 10 limit)
        with self.assertRaises(TokenLimitExceededError):
            wrapper._check_token_limits(15)
    
    def test_token_limit_per_session(self):
        """Test per-session token limit enforcement."""
        wrapper = GPTWrapper(max_tokens_per_session=100)
        wrapper.session_tokens_used = 90
        
        # This should exceed the session limit (90 + 15 = 105 > 100 limit)
        with self.assertRaises(TokenLimitExceededError):
            wrapper._check_token_limits(15)
    
    def test_chat_completion_success(self):
        """Test successful chat completion."""
        # Mock the OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_response.usage.prompt_tokens = 10
        mock_response.usage.completion_tokens = 5
        mock_response.usage.total_tokens = 15

        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        self.mock_openai.return_value = mock_client

        # Use higher limits to avoid token limit errors in test
        wrapper = GPTWrapper(max_tokens_per_request=2000, max_tokens_per_session=5000)
        messages = [{"role": "user", "content": "Hello"}]

        result = wrapper.chat_completion(messages, max_tokens=100)

        self.assertEqual(result["response"], "Test response")
        self.assertEqual(result["usage"]["total_tokens"], 15)
        self.assertEqual(wrapper.session_tokens_used, 15)
        self.assertEqual(wrapper.request_count, 1)
        self.assertGreater(result["usage"]["estimated_cost"], 0)
    
    def test_simple_chat(self):
        """Test simple chat interface."""
        # Mock the chat_completion method
        wrapper = GPTWrapper()
        wrapper.chat_completion = Mock(return_value={"response": "Simple response"})
        
        response = wrapper.simple_chat("Hello")
        
        self.assertEqual(response, "Simple response")
        wrapper.chat_completion.assert_called_once()
    
    def test_reset_session(self):
        """Test session reset functionality."""
        wrapper = GPTWrapper()
        wrapper.session_tokens_used = 100
        wrapper.session_cost = 0.05
        wrapper.request_count = 5
        
        wrapper.reset_session()
        
        self.assertEqual(wrapper.session_tokens_used, 0)
        self.assertEqual(wrapper.session_cost, 0.0)
        self.assertEqual(wrapper.request_count, 0)
    
    def test_get_session_stats(self):
        """Test session statistics retrieval."""
        wrapper = GPTWrapper(max_tokens_per_session=1000)
        wrapper.session_tokens_used = 250
        wrapper.session_cost = 0.01
        wrapper.request_count = 3
        
        stats = wrapper.get_session_stats()
        
        self.assertEqual(stats["total_tokens_used"], 250)
        self.assertEqual(stats["total_cost"], 0.01)
        self.assertEqual(stats["request_count"], 3)
        self.assertEqual(stats["remaining_tokens"], 750)
    
    def test_update_config(self):
        """Test configuration updates."""
        wrapper = GPTWrapper()
        original_model = wrapper.config["model"]
        
        wrapper.update_config(model="gpt-4", temperature=0.9)
        
        self.assertEqual(wrapper.config["model"], "gpt-4")
        self.assertEqual(wrapper.config["temperature"], 0.9)
        self.assertNotEqual(wrapper.config["model"], original_model)


if __name__ == "__main__":
    unittest.main()
